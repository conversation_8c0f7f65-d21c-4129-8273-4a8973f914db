#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
preview_manager.py

预览管理器 - 为GUI提供分块预览功能的业务逻辑
专门处理项目分块预览相关的操作，分离GUI和业务逻辑
"""

import logging
from pathlib import Path
from typing import Dict, Any, List, Optional
from dataclasses import dataclass


@dataclass
class ChunkPreview:
    """分块预览信息"""
    index: int
    length: int
    lines: int
    preview: str


@dataclass
class ProjectChunkingStatus:
    """项目分块状态"""
    project_name: str
    has_preprocessed: bool
    total_content_length: int
    effective_chunks: int
    validation_valid: bool
    validation_reason: str
    preprocessed_file_path: Optional[Path] = None


class PreviewManager:
    """
    预览管理器
    
    提供项目分块预览的业务逻辑，包括：
    - 获取项目分块状态
    - 生成分块预览
    - 分块统计信息
    """
    
    def __init__(self, processor):
        """
        初始化预览管理器
        
        Args:
            processor: DocumentProcessor实例
        """
        self.processor = processor
        
    def get_project_chunking_status(self, project_name: str) -> ProjectChunkingStatus:
        """
        获取项目的分块状态
        
        Args:
            project_name: 项目名称
            
        Returns:
            ProjectChunkingStatus: 项目分块状态
        """
        try:
            if not self.processor or not hasattr(self.processor, 'semantic_chunker'):
                raise ValueError("智能分块功能未初始化")
                
            # 查找项目的预处理文件
            base_dir = Path(self.processor.config.get('BASE_DIR', 'documents'))
            
            try:
                preprocessed_file_path = self.processor.semantic_chunker.get_preprocessed_file_path(project_name, base_dir)
            except FileNotFoundError:
                return ProjectChunkingStatus(
                    project_name=project_name,
                    has_preprocessed=False,
                    total_content_length=0,
                    effective_chunks=0,
                    validation_valid=False,
                    validation_reason="项目中没有找到markdown文件"
                )
                
            if not preprocessed_file_path.exists():
                return ProjectChunkingStatus(
                    project_name=project_name,
                    has_preprocessed=False,
                    total_content_length=0,
                    effective_chunks=0,
                    validation_valid=False,
                    validation_reason="项目尚未进行语义分块预处理"
                )
                
            # 加载预处理内容
            content = self.processor.semantic_chunker.load_preprocessed_content(preprocessed_file_path)
            if not content:
                return ProjectChunkingStatus(
                    project_name=project_name,
                    has_preprocessed=False,
                    total_content_length=0,
                    effective_chunks=0,
                    validation_valid=False,
                    validation_reason="无法读取预处理文件",
                    preprocessed_file_path=preprocessed_file_path
                )
                
            # 获取分块统计
            stats = self.processor.chunk_processor.get_chunk_statistics(content)
            
            return ProjectChunkingStatus(
                project_name=project_name,
                has_preprocessed=True,
                total_content_length=stats['total_content_length'],
                effective_chunks=stats['effective_chunks'],
                validation_valid=stats['validation']['valid'],
                validation_reason=stats['validation'].get('reason', ''),
                preprocessed_file_path=preprocessed_file_path
            )
            
        except Exception as e:
            logging.error(f"获取项目 {project_name} 分块状态失败: {e}")
            return ProjectChunkingStatus(
                project_name=project_name,
                has_preprocessed=False,
                total_content_length=0,
                effective_chunks=0,
                validation_valid=False,
                validation_reason=f"获取状态失败: {str(e)}"
            )
    
    def get_chunk_previews(self, project_name: str, preview_length: int = 100) -> List[ChunkPreview]:
        """
        获取项目的分块预览
        
        Args:
            project_name: 项目名称
            preview_length: 预览文本长度
            
        Returns:
            List[ChunkPreview]: 分块预览列表
        """
        try:
            status = self.get_project_chunking_status(project_name)
            
            if not status.has_preprocessed or not status.preprocessed_file_path:
                return []
                
            # 加载预处理内容
            content = self.processor.semantic_chunker.load_preprocessed_content(status.preprocessed_file_path)
            if not content:
                return []
                
            # 获取分块预览
            previews_data = self.processor.chunk_processor.preview_chunks(content, preview_length)
            
            # 转换为ChunkPreview对象
            previews = []
            for preview_data in previews_data:
                preview = ChunkPreview(
                    index=preview_data['index'],
                    length=preview_data['length'],
                    lines=preview_data['lines'],
                    preview=preview_data['preview']
                )
                previews.append(preview)
                
            return previews
            
        except Exception as e:
            logging.error(f"获取项目 {project_name} 分块预览失败: {e}")
            return []
    
    def reprocess_project(self, project_name: str) -> bool:
        """
        重新预处理项目（删除现有预处理文件）
        
        Args:
            project_name: 项目名称
            
        Returns:
            bool: 是否删除成功
        """
        try:
            status = self.get_project_chunking_status(project_name)
            
            if status.has_preprocessed and status.preprocessed_file_path:
                if status.preprocessed_file_path.exists():
                    status.preprocessed_file_path.unlink()
                    logging.info(f"已删除项目 {project_name} 的预处理文件")
                    return True
                    
            return False
            
        except Exception as e:
            logging.error(f"重新预处理项目 {project_name} 失败: {e}")
            return False
    
    def get_available_projects(self) -> List[str]:
        """
        获取可用的项目列表
        
        Returns:
            List[str]: 项目名称列表
        """
        try:
            if not self.processor:
                return []
                
            # 获取documents/extracted目录下的所有项目
            base_dir = Path(self.processor.config.get('BASE_DIR', 'documents'))
            extracted_dir = base_dir / 'extracted'
            
            if not extracted_dir.exists():
                return []
                
            projects = []
            for item in extracted_dir.iterdir():
                if item.is_dir():
                    projects.append(item.name)
                    
            return sorted(projects)
            
        except Exception as e:
            logging.error(f"获取项目列表失败: {e}")
            return []
    
    def preprocess_markdown_only(self, project_name: str) -> Dict[str, Any]:
        """
        只预处理Markdown文件（不生成Anki）
        
        Args:
            project_name: 项目名称
            
        Returns:
            Dict: 预处理结果
        """
        try:
            if not self.processor or not hasattr(self.processor, 'semantic_chunker'):
                raise ValueError("智能分块功能未初始化")
                
            # 查找项目的markdown文件
            base_dir = Path(self.processor.config.get('BASE_DIR', 'documents'))
            project_dir = base_dir / 'extracted' / project_name
            
            if not project_dir.exists():
                raise ValueError(f"项目目录不存在: {project_dir}")
                
            # 查找markdown文件
            md_files = list(project_dir.glob("*.md"))
            if not md_files:
                raise ValueError("项目中没有找到markdown文件")
                
            md_file = md_files[0]  # 使用第一个markdown文件
            
            # 读取markdown内容
            with open(md_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 执行语义分块预处理
            chunking_result = self.processor.semantic_chunker.preprocess_document(content, project_name)
            
            if not chunking_result.success:
                raise ValueError(f"语义分块失败: {chunking_result.error_message}")
                
            # 保存预处理结果
            preprocessed_file_path = self.processor.semantic_chunker.get_preprocessed_file_path(project_name, base_dir)
            if not self.processor.semantic_chunker.save_preprocessed_content(chunking_result.preprocessed_content, preprocessed_file_path):
                raise ValueError("预处理文件保存失败")
                
            return {
                'success': True,
                'project_name': project_name,
                'markdown_file': md_file.name,
                'file_size': len(content),
                'chunk_count': chunking_result.chunk_count,
                'processing_time': chunking_result.processing_time,
                'original_length': chunking_result.original_length,
                'preprocessed_length': chunking_result.preprocessed_length,
                'preprocessed_file': preprocessed_file_path.name
            }
            
        except Exception as e:
            logging.error(f"预处理Markdown失败: {e}")
            return {
                'success': False,
                'error_message': str(e)
            }
