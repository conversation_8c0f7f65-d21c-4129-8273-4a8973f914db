#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
preprocess_manager.py

预处理管理器 - 为GUI提供预处理功能的业务逻辑
专门处理项目预处理相关的操作，分离GUI和业务逻辑
"""

import logging
import time
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass


@dataclass
class ProjectPreprocessInfo:
    """项目预处理信息"""
    project_name: str
    status: str  # "已预处理", "未预处理", "错误"
    chunks_count: int
    last_processed: str
    preprocessed_file_path: Optional[Path] = None
    error_message: str = ""


@dataclass
class PreprocessResult:
    """预处理结果"""
    success: bool
    project_name: str
    original_file: str
    preprocessed_file: str
    chunk_count: int
    processing_time: float
    original_length: int
    preprocessed_length: int
    error_message: str = ""


class PreprocessManager:
    """
    预处理管理器
    
    提供项目预处理的业务逻辑，包括：
    - 获取项目预处理状态
    - 执行单个项目预处理
    - 批量预处理
    - 预处理结果管理
    """
    
    def __init__(self, processor):
        """
        初始化预处理管理器
        
        Args:
            processor: DocumentProcessor实例
        """
        self.processor = processor
        
    def get_projects_preprocess_status(self) -> List[ProjectPreprocessInfo]:
        """
        获取所有项目的预处理状态
        
        Returns:
            List[ProjectPreprocessInfo]: 项目预处理信息列表
        """
        projects_info = []
        
        try:
            if not self.processor:
                return projects_info
                
            # 获取项目列表
            base_dir = Path(self.processor.config.get('BASE_DIR', 'documents'))
            extracted_dir = base_dir / 'extracted'
            
            if not extracted_dir.exists():
                return projects_info
                
            for project_dir in extracted_dir.iterdir():
                if not project_dir.is_dir():
                    continue
                    
                project_name = project_dir.name
                
                try:
                    # 检查预处理状态
                    info = self._get_project_preprocess_info(project_name, project_dir)
                    projects_info.append(info)
                    
                except Exception as e:
                    logging.warning(f"检查项目 {project_name} 预处理状态失败: {e}")
                    projects_info.append(ProjectPreprocessInfo(
                        project_name=project_name,
                        status="❌ 错误",
                        chunks_count=0,
                        last_processed="-",
                        error_message=str(e)
                    ))
                    
        except Exception as e:
            logging.error(f"获取项目预处理状态失败: {e}")
            
        return sorted(projects_info, key=lambda x: x.project_name)
    
    def _get_project_preprocess_info(self, project_name: str, project_dir: Path) -> ProjectPreprocessInfo:
        """获取单个项目的预处理信息"""
        # 查找预处理文件
        preprocessed_files = list(project_dir.glob("*_preprocessed.md"))
        
        if preprocessed_files:
            # 已预处理
            preprocessed_file = preprocessed_files[0]
            
            # 获取文件修改时间
            mtime = preprocessed_file.stat().st_mtime
            last_processed = time.strftime("%Y-%m-%d %H:%M", time.localtime(mtime))
            
            # 尝试获取语义块数量
            try:
                content = preprocessed_file.read_text(encoding='utf-8')
                chunk_count = content.count('<!-- CHUNK_BREAK -->')
            except:
                chunk_count = 0
                
            return ProjectPreprocessInfo(
                project_name=project_name,
                status="✅ 已预处理",
                chunks_count=chunk_count,
                last_processed=last_processed,
                preprocessed_file_path=preprocessed_file
            )
        else:
            # 未预处理
            return ProjectPreprocessInfo(
                project_name=project_name,
                status="⚠️ 未预处理",
                chunks_count=0,
                last_processed="-"
            )
    
    def preprocess_project(self, project_name: str) -> PreprocessResult:
        """
        预处理单个项目
        
        Args:
            project_name: 项目名称
            
        Returns:
            PreprocessResult: 预处理结果
        """
        try:
            if not self.processor or not hasattr(self.processor, 'semantic_chunker'):
                raise ValueError("智能分块功能未初始化")
                
            # 查找项目的markdown文件
            base_dir = Path(self.processor.config.get('BASE_DIR', 'documents'))
            project_dir = base_dir / 'extracted' / project_name
            
            if not project_dir.exists():
                raise ValueError(f"项目目录不存在: {project_dir}")
                
            # 查找markdown文件
            md_files = list(project_dir.glob("*.md"))
            # 排除预处理文件
            md_files = [f for f in md_files if not f.name.endswith("_preprocessed.md")]
            
            if not md_files:
                raise ValueError(f"项目 '{project_name}' 中没有找到markdown文件")
                
            # 使用第一个markdown文件
            md_file = md_files[0]
            
            # 读取文件内容
            content = md_file.read_text(encoding='utf-8')
            
            # 执行语义分块预处理
            chunking_result = self.processor.semantic_chunker.preprocess_document(content, project_name)
            
            if not chunking_result.success:
                raise ValueError(f"语义分块失败: {chunking_result.error_message}")
                
            # 使用semantic_chunker的标准路径获取方法
            base_dir = Path(self.processor.config.get('BASE_DIR', 'documents'))
            preprocessed_file = self.processor.semantic_chunker.get_preprocessed_file_path(project_name, base_dir)

            if not self.processor.semantic_chunker.save_preprocessed_content(
                chunking_result.preprocessed_content, preprocessed_file):
                raise ValueError("保存预处理文件失败")
                
            return PreprocessResult(
                success=True,
                project_name=project_name,
                original_file=str(md_file),
                preprocessed_file=str(preprocessed_file),
                chunk_count=chunking_result.chunk_count,
                processing_time=chunking_result.processing_time,
                original_length=chunking_result.original_length,
                preprocessed_length=chunking_result.preprocessed_length
            )
            
        except Exception as e:
            logging.error(f"预处理项目 {project_name} 失败: {e}")
            return PreprocessResult(
                success=False,
                project_name=project_name,
                original_file="",
                preprocessed_file="",
                chunk_count=0,
                processing_time=0,
                original_length=0,
                preprocessed_length=0,
                error_message=str(e)
            )
    
    def batch_preprocess_projects(self, project_names: List[str]) -> Dict[str, Any]:
        """
        批量预处理项目
        
        Args:
            project_names: 项目名称列表
            
        Returns:
            Dict: 批量处理结果
        """
        results = []
        failed_projects = []
        
        for project_name in project_names:
            try:
                result = self.preprocess_project(project_name)
                if result.success:
                    results.append(result)
                else:
                    failed_projects.append((project_name, result.error_message))
                    
            except Exception as e:
                failed_projects.append((project_name, str(e)))
                
        return {
            'total_projects': len(project_names),
            'successful_projects': len(results),
            'failed_projects': len(failed_projects),
            'results': results,
            'failures': failed_projects
        }
    
    def get_project_preprocess_details(self, project_name: str) -> Optional[Dict[str, Any]]:
        """
        获取项目预处理详细信息
        
        Args:
            project_name: 项目名称
            
        Returns:
            Dict: 预处理详细信息，如果没有预处理文件则返回None
        """
        try:
            base_dir = Path(self.processor.config.get('BASE_DIR', 'documents'))
            project_dir = base_dir / 'extracted' / project_name
            
            # 查找预处理文件
            preprocessed_files = list(project_dir.glob("*_preprocessed.md"))
            
            if not preprocessed_files:
                return None
                
            preprocessed_file = preprocessed_files[0]
            
            # 获取文件信息
            stat = preprocessed_file.stat()
            last_modified = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(stat.st_mtime))
            
            # 读取内容获取统计信息
            try:
                content = preprocessed_file.read_text(encoding='utf-8')
                chunk_count = content.count('<!-- CHUNK_BREAK -->')
                content_length = len(content)
                line_count = content.count('\n') + 1
            except:
                chunk_count = 0
                content_length = 0
                line_count = 0
                
            return {
                'project_name': project_name,
                'preprocessed_file': preprocessed_file.name,
                'file_size': stat.st_size,
                'last_modified': last_modified,
                'chunk_count': chunk_count,
                'content_length': content_length,
                'line_count': line_count
            }
            
        except Exception as e:
            logging.error(f"获取项目 {project_name} 预处理详情失败: {e}")
            return None
    
    def delete_preprocess_file(self, project_name: str) -> bool:
        """
        删除项目的预处理文件
        
        Args:
            project_name: 项目名称
            
        Returns:
            bool: 是否删除成功
        """
        try:
            base_dir = Path(self.processor.config.get('BASE_DIR', 'documents'))
            project_dir = base_dir / 'extracted' / project_name
            
            # 查找预处理文件
            preprocessed_files = list(project_dir.glob("*_preprocessed.md"))
            
            for preprocessed_file in preprocessed_files:
                preprocessed_file.unlink()
                logging.info(f"已删除预处理文件: {preprocessed_file}")
                
            return True
            
        except Exception as e:
            logging.error(f"删除项目 {project_name} 预处理文件失败: {e}")
            return False
